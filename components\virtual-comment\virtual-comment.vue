<template>
  <u-popup v-model="show" mode="bottom" border-radius="14" :safe-area-inset-bottom="true">
    <view class="virtual-comment">
      <view class="row-between" style="padding: 30rpx">
        <view class="title md bold">添加虚拟评论</view>
        <view class="close" @tap="close">
          <image class="icon-lg" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_close.png"></image>
        </view>
      </view>
      <scroll-view scroll-y="true" style="height: 800rpx; padding: 0 30rpx;">
        <!-- 评价用户信息设置 -->
        <view class="form-item">
          <view class="item-label">评价用户信息</view>
          <view class="user-info row">
            <view class="avatar-wrap" @tap="chooseAvatar">
              <image class="avatar" :src="formData.avatar || defaultAvatar"></image>
              <view class="change-avatar">更换头像</view>
            </view>
            <view class="user-detail column">
              <input 
                class="input nickname" 
                placeholder="请输入用户昵称" 
                v-model="formData.nickname"
                placeholder-class="input-placeholder"
              />
              <picker 
                class="picker" 
                :range="levelOptions" 
                range-key="name" 
                @change="onLevelChange"
              >
                <view class="picker-text">
                  <text v-if="formData.level && levelOptions.length">{{getLevelName(formData.level)}}</text>
                  <text v-else class="input-placeholder">请选择会员等级</text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        
        <!-- 评价时间选择 -->
        <view class="form-item">
          <view class="item-label">评价时间</view>
          <picker 
            mode="date" 
            :value="formData.comment_time.split(' ')[0]" 
            @change="onDateChange"
            class="date-picker"
          >
            <view class="picker-text">
              <text v-if="formData.comment_time">{{formData.comment_time}}</text>
              <text v-else class="input-placeholder">请选择评价时间</text>
            </view>
          </picker>
        </view>
        
        <!-- 评价等级选择 -->
        <view class="form-item">
          <view class="item-label">评价等级</view>
          <view class="score-wrap">
            <!-- 使用简单的方法实现评分 -->
            <view class="rate-box">
              <view 
                v-for="i in 5" 
                :key="i" 
                class="rate-item" 
                @tap="setRate(i)"
              >
                <image 
                  class="rate-star" 
                  :src="i <= formData.score ? 'https://yinshua.zueseo.cn/static/uniapp/images/icon_collection_s.png' : 'https://yinshua.zueseo.cn/static/uniapp/images/icon_collection.png'"
                ></image>
              </view>
            </view>
            <text class="score-text ml20">{{scoreTexts[formData.score-1]}}</text>
          </view>
        </view>
        
        <!-- 评价内容编辑 -->
        <view class="form-item">
          <view class="item-label">评价内容</view>
          <textarea 
            class="comment-content" 
            placeholder="请输入评价内容" 
            v-model="formData.comment"
            placeholder-class="input-placeholder"
          ></textarea>
        </view>
        
        <!-- 评价图片上传 -->
        <view class="form-item">
          <view class="item-label">评价图片</view>
          <u-upload
            ref="uUpload"
            :file-list="fileList"
            @on-success="onUploadSuccess"
            @on-remove="onRemovePic"
            :action="uploadUrl"
            name="file"
            multiple
            :max-count="'6'"
            :image-mode="'3'"
          ></u-upload>
        </view>
      </scroll-view>
      
      <!-- 提交按钮 -->
      <view class="submit-btn">
        <view class="btn bg-primary white row-center" @tap="submitComment">提交评价</view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { addVirtualComment, getLevelList, getFileList } from '@/api/user';

export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    goodsId: {
      type: [Number, String],
      default: ''
    }
  },
  
  data() {
    return {
      show: false,
      levelOptions: [],
      fileList: [],
      defaultAvatar: 'http://yinshua.zueseo.cn/uploads/images/20250618235019fad897450.png',
      scoreTexts: ['很差', '较差', '一般', '满意', '非常满意'],
      formData: {
        goods_id: this.goodsId || '',
        avatar: '',
        nickname: '',
        level: '',
        comment_time: this.formatDate(new Date()),
        score: 5,
        comment: '',
        comment_image: []
      }
    };
  },
  
  computed: {
    uploadUrl() {
        if (process.env.NODE_ENV === 'development') {
            return 'http://www.likeshop-server.com/api/file/formimage';
        }
        return '/api/file/formimage';
    }
  },
  
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit('input', val);
    },
    goodsId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.formData.goods_id = val;
        }
      }
    }
  },
  
  created() {
    // 获取会员等级选项
    this.getLevelOptions();
    this.formData.avatar = this.defaultAvatar;
  },
  
  methods: {
    // 设置默认的会员等级选项
    setDefaultLevelOptions() {
      // 获取失败时使用默认数据
      this.levelOptions = [
        { id: '普通会员', name: '普通会员' },
        { id: '黄金会员', name: '黄金会员' },
        { id: '铂金会员', name: '铂金会员' },
        { id: '钻石会员', name: '钻石会员' },
        { id: '评论家', name: '评论家' }
      ];
      // 确保设置默认等级
      this.formData.level = this.levelOptions[0].id;
    },
    
    // 获取会员等级选项
    getLevelOptions() {
      getLevelList().then(res => {
        // 根据用户反馈, aip返回 code 1, 数据在 data.level_list
        if (res.code === 1 && res.data && res.data.level_list) {
          this.levelOptions = res.data.level_list.map(item => ({
            id: item.name, // 使用name作为ID
            name: item.name
          }));
          // 确保设置默认等级
          if (this.levelOptions.length > 0 && !this.formData.level) {
            this.formData.level = this.levelOptions[0].id;
          }
        } else {
          this.setDefaultLevelOptions();
        }
      }).catch(() => {
        this.setDefaultLevelOptions();
      });
    },
    
    // 关闭弹窗
    close() {
      this.show = false;
      this.resetForm();
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        goods_id: this.goodsId,
        avatar: this.defaultAvatar,
        nickname: '',
        level: '',
        comment_time: this.formatDate(new Date()),
        score: 5,
        comment: '',
        comment_image: []
      };
      if (this.levelOptions.length) {
          this.formData.level = this.levelOptions[0].id
      }
      this.fileList = [];
      this.$refs.uUpload.clear();
    },
    
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.uploadFile(res.tempFilePaths[0], 'avatar');
        }
      });
    },
    
    // 上传文件
    uploadFile(filePath, type = 'comment') {
      uni.uploadFile({
        url: this.$BASE_URL + '/api/file/formimage',
        filePath,
        name: 'file',
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code === 1) {
            const url = data.data.url;
            if (type === 'avatar') {
              this.formData.avatar = url;
            } else {
              this.formData.comment_image.push(url);
            }
          } else {
            this.$toast({ title: data.msg });
          }
        },
        fail: () => {
          this.$toast({ title: '上传失败' });
        }
      });
    },
    
    onUploadSuccess(data, index, lists, name) {
        if (data.code === 1) {
            this.formData.comment_image.push(data.data.url);
        } else {
            this.$toast({ title: data.msg });
        }
        this.fileList = lists;
    },

    onRemovePic(index, lists, name) {
        this.formData.comment_image.splice(index, 1);
        this.fileList = lists;
    },
    
    // 选择会员等级
    onLevelChange(e) {
      const index = e.detail.value;
      this.formData.level = this.levelOptions[index].id;
    },
    
    // 根据ID获取等级名称
    getLevelName(id) {
      const level = this.levelOptions.find(item => item.id === id);
      return level ? level.name : '';
    },
    
    // 选择日期时间
    onDateChange(e) {
      const date = e.detail.value;
      this.formData.comment_time = date + ' ' + this.getCurrentTime();
    },
    
    // 获取当前时间
    getCurrentTime() {
      const date = new Date();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    
    // 校验表单
    validateForm() {
      if (!this.formData.avatar) {
        this.formData.avatar = this.defaultAvatar;
      }
      if (!this.formData.nickname) {
        this.$toast({ title: '请填写昵称' });
        return false;
      }
      if (!this.formData.level) {
        this.$toast({ title: '请选择会员等级' });
        return false;
      }
      if (!this.formData.comment_time) {
        this.$toast({ title: '请选择评价时间' });
        return false;
      }
      if (!this.formData.score) {
        this.$toast({ title: '请选择评价等级' });
        return false;
      }
      if (!this.formData.comment) {
        this.$toast({ title: '请填写评价内容' });
        return false;
      }
      return true;
    },
    
    // 选择评分等级
    setRate(score) {
      this.formData.score = score;
    },
    
    // 提交评价
    async submitComment() {
      if (!this.validateForm()) return;
      
      // 确保商品ID已设置
      if (!this.formData.goods_id && this.goodsId) {
        this.formData.goods_id = this.goodsId;
      }
      
      try {
        const { code, msg } = await addVirtualComment(this.formData);
        if (code === 1) {
          this.$toast({ title: '评价添加成功', icon: 'success' });
          this.close();
          this.$emit('success');
        } else {
          this.$toast({ title: msg });
        }
      } catch (error) {
        this.$toast({ title: '评价添加失败' });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.virtual-comment {
  padding-bottom: calc(130rpx + env(safe-area-inset-bottom));
  
  .form-item {
    margin-bottom: 30rpx;
    
    .item-label {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
  }
  
  .user-info {
    .avatar-wrap {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      overflow: hidden;
      
      .avatar {
        width: 100%;
        height: 100%;
      }
      
      .change-avatar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 20rpx;
        text-align: center;
        height: 40rpx;
        line-height: 40rpx;
      }
    }
    
    .user-detail {
      flex: 1;
      margin-left: 20rpx;
      
      .input {
        height: 60rpx;
        line-height: 60rpx;
        border-bottom: 1px solid #f2f2f2;
        margin-bottom: 10rpx;
      }
      
      .picker {
        height: 60rpx;
        line-height: 60rpx;
        border-bottom: 1px solid #f2f2f2;
      }
    }
  }
  
  .date-picker {
    height: 80rpx;
    line-height: 80rpx;
    border-bottom: 1px solid #f2f2f2;
  }
  
  .score-wrap {
    padding: 10rpx 0;
    display: flex;
    align-items: center;
    
    .rate-box {
      display: flex;
    }
    
    .rate-item {
      padding: 5rpx;
    }
    
    .rate-star {
      width: 40rpx;
      height: 40rpx;
    }
    
    .score-text {
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .comment-content {
    width: 100%;
    height: 200rpx;
    padding: 20rpx;
    background: #f8f8f8;
    border-radius: 8rpx;
    box-sizing: border-box;
  }
  
  .submit-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 9;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    
    .btn {
      height: 90rpx;
      border-radius: 45rpx;
      font-size: 32rpx;
    }
  }
  
  .input-placeholder {
    color: #999;
  }
  
  .picker-text {
    height: 60rpx;
    line-height: 60rpx;
  }
}
</style> 