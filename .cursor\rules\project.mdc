---
description: 
globs: 
alwaysApply: true
---
### Uni-app 项目代码规范最佳实践

本项目基于 `uni-app` 框架和 `uView UI` 组件库。为了确保代码质量、可维护性和团队协作效率，特制定以下代码规范。

#### 1. 组件化规范

*   **组件目录结构**：
    *   将所有组件统一放在 `components` 目录中，并按功能进行分类。
    *   示例：`src/components/common/` (公共组件), `src/components/user/` (用户模块相关组件)。
    *   建议每个文件夹下组件数量不宜过多，保证分类清晰。
    *   根据组件的作用范围进行分层级结构，如 `common` 存放通用组件， `feature` 存放特定功能模块的组件。

*   **组件命名**：
    *   **组件文件名**：使用大驼峰命名法（`PascalCase`），与组件名保持一致，例如 `UserProfile.vue`。
    *   **组件名称**（`name` 字段）：使用大驼峰命名法，保持与文件名一致，方便调试和排查。
    *   命名建议：
        *   使用名词或名词短语，表示组件的功能或内容。
        *   对于复用性高的组件，如 `Button`、`Input`，应保持名称简洁明确。
        *   对于特定模块的组件，可以在名称中体现模块名，例如 `UserList`、`ProductCard`。

*   **组件类型划分**：
    *   **基础组件（Base Components）**：纯 UI 组件，无业务逻辑。命名通常以 `Base` 开头。
    *   **业务组件（Business Components）**：与业务逻辑相关，包含特定模块功能。
    *   **页面级组件（Page Components）**：用于构建整个页面，通常在 `pages` 目录下管理。

*   **组件内结构规范**：
    *   **代码顺序**：
        1.  `name`
        2.  `props`
        3.  `data`
        4.  `computed`
        5.  `methods`
        6.  `watch`
        7.  `lifecycle hooks`
    *   **Props 规范**：
        *   必须明确 `type` 类型，并添加 `required` 字段。
        *   使用 `default` 字段提供默认值。

*   **组件通信**：
    *   **父子组件通信**：通过 `props` 传递数据，`$emit` 触发事件。
    *   **非父子组件通信**：建议使用 `Vuex` 进行全局状态管理。

*   **组件样式规范**：
    *   **局部样式作用域**：使用 `<style scoped>` 确保组件样式局部化，避免样式污染。
    *   **样式命名**：推荐使用 BEM（Block Element Modifier）命名法。

*   **组件重用**：
    *   **公共组件**：提取复用率高的组件，形成独立的公共组件库。
    *   **复用方法与逻辑**：对于共享逻辑，可以使用 `mixin`。

#### 2. HTML 规范

*   **页面语言**：推荐使用 `lang=”zh-CN”`。
*   **元素及标签闭合**：所有具有开始标签和结束标签的元素都要写上起止标签，空元素标签不加 `/` 字符。
*   **书写风格**：HTML 标签名、类名、标签属性和大部分属性值统一用小写。
*   **元素属性**：属性值使用双引号，能写上的都写上。
*   **特殊字符**：使用字符实体转义特殊字符（如 `<` 和 `>`）。
*   **段落元素与标题元素**：只能嵌套内联元素。
*   **选择器**：尽量少用通用选择器 `*`，不使用 ID 选择器，不使用无具体语义定义的标签选择器。

#### 3. CSS 规范

*   **格式化**：代码需要格式化后提交。
*   **大小写**：CSS 代码禁止大写。
*   **选择器**：尽量少使用选择器。
*   **缩进**：遵循代码格式化文件中的缩进规则。
*   **颜色值**：不需要有空格，且取值不要带不必要的 0。
*   **十六进制**：属性值十六进制数值能用简写尽量简写。
*   **单位**：不要为 0 指定单位。

#### 4. JavaScript (ES6+) 规范

*   **命名规范**：
    *   **文件夹命名**：语义化，使用中划线（kebab-case），例如 `home-page`。
    *   **文件命名**：语义化，使用中划线（kebab-case），例如 `net.js`。
    *   **`className` 命名**：使用中划线（kebab-case），例如 `home-title`。
    *   **变量命名**：小驼峰法（`camelCase`），例如 `homePage`。
    *   **函数命名**：小驼峰法，构造函数大驼峰。
    *   **常量**：全部大写。
    *   **类**：公共属性和方法同变量名，私有属性和方法前缀为下划线 `_`。

*   **引用**：
    *   对所有引用都使用 `const`，如果引用可变动，则使用 `let`，不使用 `var`。

*   **对象**：使用字面量方式创建对象。

*   **箭头函数**：
    *   当必须使用函数表达式或传递匿名函数时，使用箭头函数。
    *   单行函数且只有一个参数时，省略花括号、圆括号和 `return`。

*   **构造器**：
    *   总是使用 `class`，避免直接操作 `prototype`。
    *   使用 `extends` 继承。
    *   方法可以返回 `this` 实现链式调用。
    *   可自定义 `toString()` 方法，确保其正常运行且无副作用。

*   **模块**：
    *   使用模组（`import`/`export`），不使用通配符 `import`。
    *   不要从 `import` 中直接 `export`。

*   **属性**：
    *   使用 `.` 来访问对象的属性。
    *   当通过变量访问属性时使用中括号 `[]`。

*   **比较运算符和等号**：
    *   优先使用 `===` 和 `!==` 而不是 `==` 和 `!=`。
    *   条件表达式遵循 JavaScript 的 ToBoolean 强制计算规则。

#### 5. Vue 特定规范

*   **组件名为多个单词**：除了根组件 `App`，组件名应为多个单词（例如 `TodoList` 而不是 `Todo`）。
*   **组件数据**：组件 `data` 必须是一个返回对象的函数（除了根实例 `new Vue` 外）。
*   **Prop 定义**：`Prop` 定义应尽量详细，至少需要指定其类型，推荐使用 `required` 和 `validator`。
*   **为 `v-for` 设置键**：总是用 `key` 配合 `v-for` 使用。
*   **避免 `v-if` 和 `v-for` 一起使用**：
    *   过滤列表项目：将过滤逻辑放入计算属性中。
    *   避免渲染隐藏列表：将 `v-if` 移动到容器元素上。
*   **为组件的样式设置作用域**：除了顶级 `App` 组件和布局组件，其他所有组件的样式都应该是有作用域的（使用 `<style scoped>` 或 CSS Modules）。
*   **私有属性名**：使用 `$_` 前缀作为用户定义的私有属性约定，避免与 Vue 自身冲突。

#### 6. 性能优化 (uni-app x 相关，可作为参考)

*   **DOM 数量和层级**：尽量减少 DOM 元素数量和层级，特别是在 Android 平台。
*   **界面元素动画**：
    *   固定动画使用 CSS `transition` 或 `animation-view` 组件。
    *   跟随手势的动效，使用 `transform` 方式移动 DOM 元素，并直接调用 JS API 操作，而不是通过模板 `style` 绑定 `data`。
*   **避免复杂逻辑卡 UI**：耗时操作（如联网取数）应在合适的生命周期（如 `onLoad` 或 `onReady`）中进行，并注意避免阻塞 UI 主线程。
*   **长列表**：使用 `list-view` 或 `waterflow` 组件进行复用，或者考虑分批加载、`uni-recycle-view` 组件。
*   **优化排版效率**：
    *   指定明确的宽高，减少自适应计算。
    *   指定主轴方向的尺寸。
    *   给 `text` 组件指定宽高，指定图片的尺寸信息。
    *   CSS 单位尺寸性能：`px` > `rpx` > 百分比。
*   **控制 Vue 的更新范围**：减少对大 `data` 的修改，将可动态更新的小数据单独存放。将可点击的收藏按钮等独立为单独组件，避免引发大量 UI 重绘。
*   **避免不必要的组件抽象**：在大型列表中，避免不必要的组件抽象，减少组件实例的创建。不要对 `view`、`text` 等基础组件进行过度封装。


