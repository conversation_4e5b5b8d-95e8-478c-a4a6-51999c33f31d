<template>
	<view class="card-style-selector">
		<view class="style-buttons">
			<view 
				class="style-btn"
				:class="{ active: selectedStyle === style.id }"
				v-for="style in styleList" 
				:key="style.id"
				@tap="selectStyle(style)"
			>
				{{ style.name }}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CardStyleSelector',
	props: {
		styleList: {
			type: Array,
			default: () => [
				{ id: 1, name: '样式1' },
				{ id: 2, name: '样式2' },
				{ id: 3, name: '样式3' },
				{ id: 5, name: '样式5' }
			]
		},
		selectedStyle: {
			type: [Number, String],
			default: 1
		}
	},
	methods: {
		selectStyle(style) {
			this.$emit('change', style);
		}
	}
}
</script>

<style lang="scss" scoped>
.card-style-selector {
	background: #fff;
	padding: 24rpx;
	
	.style-buttons {
		display: flex;
		gap: 20rpx;
		
		.style-btn {
			flex: 1;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 2rpx solid #e0e0e0;
			border-radius: 8rpx;
			font-size: 28rpx;
			color: #666;
			background: #fff;
			transition: all 0.3s;
			
			&.active {
				background: #ff2c3c;
				color: #fff;
				border-color: #ff2c3c;
			}
			
			&:first-child.active {
				background: #ff2c3c;
			}
		}
	}
}
</style>