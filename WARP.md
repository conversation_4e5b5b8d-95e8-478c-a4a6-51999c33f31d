# WARP.md

此文件为 WARP (warp.dev) 在此代码库中工作时提供指导。

## 项目概述

这是 **LikeShop**，一个基于 uni-app 框架（版本 3.3.13）构建的综合性电商应用，支持多个平台包括微信小程序、H5、iOS 和 Android 应用。项目采用模块化架构，以 Vue.js 作为前端框架。

## 常用开发命令

### 构建和开发
```bash
# 安装依赖（如果 package.json 中有依赖）
npm install

# 构建不同平台的项目
npm run build          # 执行自动构建脚本
./autoRelease.sh       # 手动构建 H5（复制到 ../server/public/mobile）

# 平台特定开发
# 使用 HBuilderX 进行 uni-app 开发和跨平台预览
```

### 测试和部署
```bash
# 目前未配置测试脚本 - 需要时可添加测试

# 检查构建输出
ls unpackage/dist/build/web/    # H5 构建输出
ls unpackage/                   # 各平台特定输出
```

## 架构概述

### 核心结构
- **框架**: uni-app 3.3.13 (基于 Vue.js 的跨平台框架)
- **状态管理**: Vuex store 模式
- **HTTP 客户端**: 带拦截器的自定义 axios 封装
- **UI 组件**: uView-ui 组件库 + 自定义组件
- **多平台支持**: 微信小程序、H5、iOS/Android 应用

### 主要目录
```
/api/           - 按功能组织的 API 接口定义
/components/    - 可复用的 Vue 组件和 UI 库
/config/        - 应用配置和环境设置
/pages/         - 遵循 uni-app 路由的应用页面
/bundle/pages/  - 代码分包的子包页面
/store/         - Vuex 状态管理模块
/utils/         - 工具函数和辅助方法
/mixins/        - Vue mixins 共享功能
/static/        - 静态资源和图片
/styles/        - 全局 SCSS 样式表
```

### API 架构
API 层按功能域组织：
- `api/app.js` - 身份验证、支付、系统配置
- `api/user.js` - 用户管理、个人资料、地址、订单
- `api/store.js` - 商品目录、购物车、搜索
- `api/order.js` - 订单处理和管理
- `api/activity.js` - 营销活动（秒杀、拼团等）

### 配置管理
- `config/app.js` - 环境特定的 API 端点和应用设置
- `manifest.json` - uni-app 平台配置和功能
- `pages.json` - 页面路由和导航配置
- `vue.config.js` - 开发代理设置

### 状态管理模式
使用 Vuex 模块化方法：
- `/store/index.js` 中的集中式 store
- `/store/modules/` 中的功能特定模块
- `/store/getters.js` 中的计算 getters
- 身份验证状态和用户会话管理

### 组件架构
- **uView UI 库**: 综合性 UI 组件系统
- **自定义组件**: 业务特定的可复用组件
- **页面组件**: 带路由的单个页面实现
- **子包**: 更好性能的代码分割 (bundle/)

### 多平台考虑
- **条件编译**: 使用 `#ifdef/#endif` 进行平台特定代码
- **客户端检测**: utils/tools.js 中的自动平台检测
- **响应式设计**: 适应不同屏幕尺寸和平台
- **平台 API**: 利用各平台的原生功能

### HTTP 请求流程
1. 请求拦截器添加身份验证令牌和验证参数
2. 自定义 axios 实例处理平台特定头信息
3. 响应拦截器管理错误状态和身份验证流
4. 未授权请求的自动登录重定向

### 身份验证系统
- **多平台登录**: 微信 OAuth、短信、账户登录
- **令牌管理**: 自动令牌刷新和存储
- **会话处理**: 跨应用启动的持久登录状态
- **授权**: 不同功能的基于角色的访问控制

### 业务功能架构
- **电商核心**: 商品目录、购物车、订单处理
- **用户系统**: 注册、个人资料、地址、偏好设置
- **支付集成**: 微信支付、支付宝支持
- **营销工具**: 优惠券、秒杀、拼团、分销
- **内容管理**: 文章、广告、直播

## 开发指南

### 环境设置
1. 使用 HBuilderX IDE 获得最优的 uni-app 开发体验
2. 在 `config/app.js` 中为您的环境配置正确的 API 端点
3. 在 `vue.config.js` 中设置本地开发 API 调用的代理
4. 确保 `manifest.json` 中的平台特定配置正确

### 代码组织
- 遵循 uni-app 约定的页面结构和路由
- 使用 Vuex 管理需要在组件间共享的状态
- 在 API 调用中使用 try-catch 块实现适当的错误处理
- 利用 `/utils/` 中现有的工具函数进行常用操作

### 平台开发
- 在开发过程中在所有目标平台上进行测试
- 使用条件编译指令进行平台特定代码
- 添加功能时考虑平台限制和功能
- 遵循平台特定的设计指导原则（微信、iOS、Android）

### API 集成
- 使用 `/utils/request.js` 中现有的请求封装
- 遵循按功能域建立的 API 组织模式
- 实现适当的错误处理和用户反馈
- 适当缓存 API 响应以提高性能

### 组件开发
- 扩展现有组件库而不是从头开始创建
- 遵循 Vue.js 组件设计和 props 的最佳实践
- 实现适当的 prop 验证和默认值
- 设计组件时考虑可复用性和可维护性

### 性能考虑
- 利用 uni-app 的子包功能进行代码分割
- 实现适当的图片优化和懒加载
- 使用 Cache 工具缓存经常访问的数据
- 监控和优化不同平台的包大小

## 需要理解的重要文件

- `main.js` - 应用启动和全局配置
- `App.vue` - 带应用级生命周期钩子的根组件
- `utils/request.js` - HTTP 客户端配置和拦截器
- `utils/tools.js` - 常用操作的工具函数
- `mixins/app.js` - 页面间共享的功能
- `autoRelease.sh` - 自动化构建和部署脚本

## 开发注意事项

- 项目使用 SCSS 进行样式化，全局样式位于 `/styles/`
- `/components/custom-image/` 中提供自定义图片优化组件
- 内置加载状态和错误处理模式
- 微信特定功能需要在 manifest.json 中配置正确的 appid
- H5 部署通过构建脚本自动同步到 `../server/public/mobile/`

<citations>
<document>
<document_type>RULE</document_type>
<document_id>Eh9KesrbA265AkxlOCnj5n</document_id>
</document>
</citations>
