<template>
	<view class="user-list">
		<view class="title">下单用户</view>
		<scroll-view class="user-scroll" scroll-x="true" show-scrollbar="false">
			<view class="user-container">
				<view 
					class="user-item" 
					v-for="(user, index) in userList" 
					:key="index"
				>
					<image 
						class="avatar" 
						:src="user.avatar || '/static/images/avatar/default.png'"
						mode="aspectFill"
					/>
					<view class="username">{{ user.name || '用户名' }}</view>
				</view>
				<view class="more-btn" v-if="userList.length >= 6">
					<view class="more-text">更多</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: 'UserList',
	props: {
		userList: {
			type: Array,
			default: () => []
		}
	}
}
</script>

<style lang="scss" scoped>
.user-list {
	background: #fff;
	padding: 24rpx;
	
	.title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.user-scroll {
		width: 100%;
		white-space: nowrap;
	}
	
	.user-container {
		display: flex;
		align-items: center;
		
		.user-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-right: 30rpx;
			flex-shrink: 0;
			
			.avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-bottom: 10rpx;
			}
			
			.username {
				font-size: 24rpx;
				color: #666;
				text-align: center;
				width: 80rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		
		.more-btn {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 80rpx;
			height: 80rpx;
			background: #f5f5f5;
			border-radius: 50%;
			margin-bottom: 34rpx;
			
			.more-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}
</style>