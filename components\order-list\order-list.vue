// +---------------------------------------------------------------------- // |
likeshop开源商城系统 //
+---------------------------------------------------------------------- // |
欢迎阅读学习系统程序代码，建议反馈是我们前进的动力 // |
gitee下载：https://gitee.com/likeshop_gitee // |
github下载：https://github.com/likeshop-github // |
访问官网：https://www.likeshop.cn // | 访问社区：https://home.likeshop.cn // |
访问手册：http://doc.likeshop.cn // | 微信公众号：likeshop技术社区 // |
likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |
likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布 // |
likeshop团队版权所有并拥有最终解释权 //
+---------------------------------------------------------------------- // |
author: likeshop.cn.team //
+----------------------------------------------------------------------

<template>
  <view>
    <view class="order-list">
      <navigator
        v-for="(item, index) in orderList"
        :key="index"
        hover-class="none"
        class="order-item bg-white mt20"
        :url="'/pages/order_details/order_details?id=' + item.id"
      >
        <view class="order-header row-between">
          <view class="row">
            <view v-if="item.delivery_type == 2" class="mr10">
              <u-tag
                text="自提"
                size="mini"
                type="primary"
                mode="dark"
                bg-color="#0cc21e"
              />
            </view>
            <view v-if="item.order_type == 1" class="mr10">
              <u-tag text="秒杀" size="mini" type="primary" mode="plain" />
            </view>
            <view v-if="item.order_type == 2" class="mr10">
              <u-tag text="拼团" size="mini" type="primary" mode="plain" />
            </view>
            <view v-if="item.order_type == 3" class="mr10">
              <u-tag text="砍价" size="mini" type="primary" mode="plain" />
            </view>
            订单编号：{{ item.order_sn }}
          </view>
          <view :class="item.order_status == 4 ? 'muted' : 'primary'">{{
            item.order_status_desc
          }}</view>
        </view>
        <view class="order-con">
          <order-goods
            :list="item.order_goods"
            :order_type="item.order_type"
          ></order-goods>
          <view class="all-price row-end">
            <text class="muted xs"
              >共{{ goodCount(item.order_goods) }}件商品，总金额：</text
            >
            <price-format
              :subscript-size="30"
              :first-size="30"
              :second-size="30"
              :price="item.order_amount"
            ></price-format>
          </view>
        </view>
        <view
          class="order-footer row"
          v-if="
            item.pickup_btn ||
            item.cancel_btn ||
            item.delivery_btn ||
            item.take_btn ||
            item.del_btn ||
            item.pay_btn ||
            item.comment_btn
          "
        >
          <view style="flex: 1">
            <view
              class="primary sm row"
              style="line-height: 26rpx"
              v-if="getCancelTime(item.order_cancel_time) > 0"
              ><u-count-down
                separator="zh"
                :timestamp="getCancelTime(item.order_cancel_time)"
                separator-color="#FF2C3C"
                color="#FF2C3C"
                :separator-size="26"
                :font-size="26"
                bg-color="transparent"
                @end="reflesh"
              ></u-count-down
            ></view>
          </view>
          <view v-if="item.cancel_btn">
            <button
              size="sm"
              class="plain br60 lighter"
              hover-class="none"
              @tap.stop="cancelOrder(item.id)"
            >
              取消订单
            </button>
          </view>
          <view
            v-if="item.delivery_btn"
            @tap.stop="
              goPage(
                '/bundle/pages/goods_logistics/goods_logistics?id=' + item.id
              )
            "
          >
            <button size="sm" class="btn plain br60 lighter" hover-class="none">
              查看物流
            </button>
          </view>
          <view v-if="item.del_btn">
            <button
              size="sm"
              class="btn plain br60 lighter"
              hover-class="none"
              @tap.stop="delOrder(item.id)"
            >
              删除订单
            </button>
          </view>
          <view v-if="item.pay_btn" class="ml20">
            <button
              size="sm"
              class="btn bg-primary br60 white"
              @tap.stop="payNow(item.id)"
            >
              立即付款
            </button>
          </view>
          <view v-if="item.comment_btn" class="ml20">
            <button
              size="sm"
              hover-class="none"
              class="btn plain btn br60 primary red"
            >
              去评价
            </button>
          </view>
          <view v-if="item.pickup_btn" class="ml20">
            <button
              size="sm"
              hover-class="none"
              class="btn plain btn br60 primary red"
            >
              查看提货码
            </button>
          </view>
          <view v-if="item.take_btn" class="ml20">
            <button
              size="sm"
              class="btn plain br60 primary red"
              hover-class="none"
              @tap.stop="comfirmOrder(item.id, item.pay_way)"
            >
              确认收货
            </button>
          </view>
        </view>
      </navigator>
      <loading-footer :status="status" :slot-empty="true" @refresh="reload">
        <view slot="empty" class="column-center" style="padding-top: 200rpx">
          <image class="img-null" src="https://yinshua.zueseo.cn/static/uniapp/images/goods_null.png"></image>
          <text class="lighter">暂无订单</text>
        </view>
      </loading-footer>
    </view>
    <order-dialog
      ref="orderDialog"
      :order-id="orderId"
      :type="type"
      @refresh="reflesh"
    ></order-dialog>
    <loading-view
      v-if="showLoading"
      background-color="transparent"
      :size="50"
    ></loading-view>
  </view>
</template>

<script>
import {
  getOrderList,
  cancelOrder,
  delOrder,
  confirmOrder,
  getwxReceiveDetail,
  getwechatSyncCheck,
} from "@/api/order";
import { compareWeChatVersion } from "@/utils/tools";

import { prepay } from "@/api/app";
import { loadingType } from "@/utils/type";

import { wxpay, alipay } from "@/utils/pay";
import { loadingFun } from "@/utils/tools";
export default {
  data() {
    return {
      page: 1,
      orderList: [],
      status: loadingType.LOADING,
      showCancel: false,
      type: 0,
      orderId: "",
      showLoading: false,
      pay_way: "",
    };
  },

  components: {},
  props: {
    orderType: {
      type: String,
    },
  },
  created: function () {
    uni.$on("refreshorder", () => {
      this.reflesh();
    });
    uni.$on("payment", (params) => {
      if (params.result) {
        this.reflesh();
        uni.navigateBack();
        setTimeout(() => this.$toast({ title: "支付成功" }), 0.5 * 1000);
      }
    });
  },
  beforeMount: function () {
    this.getOrderListFun();
  },
  destroyed: function () {
    uni.$off(["payment", "refreshorder"]);
  },
  methods: {
    reflesh() {
      this.page = 1;
      this.orderList = [];
      this.status = loadingType.LOADING;
      this.type = 0;
      this.getOrderListFun();
    },

    reload() {
      this.status = loadingType.LOADING;
      this.getOrderListFun();
    },

    orderDialog() {
      this.$refs.orderDialog.open();
    },

    delOrder(id) {
      this.orderId = id;
      this.type = 1;
      this.$nextTick(() => {
        this.orderDialog();
      });
    },
    // 小程序确认收货
    comfirmReceive(transaction_id) {
      return new Promise((resolve, reject) => {
        wx.openBusinessView({
          businessType: "weappOrderConfirm",
          extraData: {
            transaction_id,
          },
          success({ extraData }) {
            if (extraData.status == "success") {
              resolve("确认收货");
            } else {
              resolve("取消收货");
            }
          },
          fail(err) {
            reject(err);
          },
        });
      });
    },
    //查询是否收货成功
    querycomfirmReceive(id) {
      return new Promise((resolve, reject) => {
        getwechatSyncCheck({ id })
          .then(({ data }) => {
            if (data.order.order_state === 4) {
              resolve("已确认收货");
            } else {
              reject("未确认收货");
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    comfirmOrder(id, pay_way) {
      this.orderId = id;
      this.pay_way = pay_way;
      this.type = 2;
      this.$nextTick(async () => {
        // #ifdef MP-WEIXIN
        let res = {};
        if (this.pay_way === 1) {
          res = await getwechatSyncCheck({ id: this.orderId });
          console.log(res);
        }
        if (
          compareWeChatVersion("2.6.0") === 1 &&
          wx.openBusinessView &&
          this.pay_way === 1 &&
          res.data.order.order_state !== 1
        ) {
          try {
            const { data } = await getwxReceiveDetail({
              order_id: this.orderId,
            });
            await this.comfirmReceive(data.transaction_id);
            await this.querycomfirmReceive(this.orderId);
            await confirmOrder(this.orderId);
          } catch (error) {
            console.log(error);
          }
          this.reflesh();
        } else {
          this.orderDialog();
        }
        // #endif

        // #ifndef MP-WEIXIN
        this.orderDialog();
        // #endif
      });
    },

    cancelOrder(id) {
      this.orderId = id;
      this.type = 0;
      this.$nextTick(() => {
        this.orderDialog();
      });
    },

    payNow(id) {
      // this.showLoading = true

      uni.navigateTo({
        url: `/pages/payment/payment?from=${"order"}&order_id=${id}`,
      });

      // prepay({
      // 	from: 'order',
      // 	order_id: id
      // }).then(res => {
      // 	let args = res.data;
      // 	this.showLoading = false
      // 	if (res.code == 1) {
      // 		wxpay(args).then((resPay) => {
      // 			if(resPay == 'success') {
      // 				this.$toast({
      // 					title: "支付成功"
      // 				})
      // 				uni.$emit("refreshorder")
      // 			}
      // 		})
      // 	}else if(res.code == 20001){
      // 		alipay(args).then((resPay) => {
      // 			if(resPay == 'success') {
      // 				this.$toast({
      // 					title: "支付成功"
      // 				})
      // 				uni.$emit("refreshorder")
      // 			}
      // 		})
      // 	}
      // });
    },

    async getOrderListFun() {
      let { page, orderType, orderList, status } = this;
      const data = await loadingFun(getOrderList, page, orderList, status, {
        type: orderType,
      });
      if (!data) return;
      this.page = data.page;
      this.orderList = data.dataList;
      this.status = data.status;
    },
    goPage(url) {
      uni.navigateTo({
        url,
      });
    },
    goodCount(goodLists) {
      console.log(goodLists);
      let count = 0;
      goodLists.forEach((item) => {
        count += item.goods_num;
      });
      return count;
    },
  },
  computed: {
    getOrderStatus() {
      return (status) => {
        let text = "";
        switch (status) {
          case 0:
            text = "待支付";
            break;
          case 1:
            text = "待发货";
            break;
          case 2:
            text = "待收货";
            break;
          case 3:
            text = "已完成";
            break;
          case 4:
            text = "订单已关闭";
            break;
        }
        return text;
      };
    },
    getCancelTime() {
      return (time) => time - Date.now() / 1000;
    },
  },
};
</script>
<style lang="scss">
.order-list {
  // min-height: calc(100vh - 80rpx);
  padding: 0 20rpx;
  overflow: hidden;

  .order-item {
    border-radius: 10rpx;

    .order-header {
      height: 80rpx;
      padding: 0 24rpx;
      border-bottom: 1px dotted #e5e5e5;
    }

    .all-price {
      text-align: right;
      padding: 0 24rpx 20rpx;
    }

    .order-footer {
      height: 100rpx;
      border-top: $-solid-border;
      padding: 0 24rpx;

      .plain {
        border: 1px solid #bbbbbb;

        &.red {
          border-color: $-color-primary;
        }
      }
    }
  }
}
</style>
