<template>
	<view class="group-users bg-white mt20" v-if="shouldShow">
		<!-- 标题部分 -->
		<view class="title-section row-between">
			<view class="title-left row">
				<view class="title-icon row-center">
					<image class="icon-sm" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_group.png"></image>
				</view>
				<view class="title-content">
					<view class="title-text md bold">已拼团用户</view>
					<view class="subtitle xxs lighter" v-if="totalUsers > 0">已有 {{ totalUsers }} 人参与拼团</view>
				</view>
			</view>
			<view class="view-all xxs primary" v-if="layout === 'list' && totalUsers > maxDisplay" @tap="handleViewMore">
				查看全部 {{ totalUsers }} 人
				<image class="icon-xs ml5" src="https://yinshua.zueseo.cn/static/uniapp/images/arrow_right.png"></image>
			</view>
		</view>
		
		<!-- 列表布局 -->
		<view class="users-content" v-if="layout === 'list' && displayUsers.length > 0">
			<view class="user-item" v-for="(user, index) in displayUsers" :key="user.id || index">
				<!-- 用户头像和信息 -->
				<view class="user-info row">
					<custom-image 
						:src="user.avatar || '/static/images/default_avatar.png'" 
						width="60rpx" 
						height="60rpx" 
						radius="50%"
						:show-error="false"
						class="user-avatar">
					</custom-image>
					<view class="user-details">
						<view class="username sm normal line1">{{ user.nickname || '匿名用户' }}</view>
						<view class="join-info xxs lighter">{{ formatJoinTime(user.join_time) }}</view>
					</view>
				</view>
				
				<!-- 用户状态 -->
				<view class="user-status">
					<view class="status-tag xxs" :class="getStatusClass(user.status)">
						{{ getStatusText(user.status) }}
					</view>
				</view>
			</view>
		</view>
		
		<!-- 网格布局 -->
		<view class="users-grid" v-if="layout === 'grid' && (gridDisplayUsers.length > 0 || totalUsers === 0)">
			<view class="grid-container">
				<!-- 用户头像网格 -->
				<view class="grid-item" v-for="(user, index) in gridDisplayUsers" :key="user.id || index">
					<view class="grid-user">
						<custom-image 
							:src="user.avatar || '/static/images/default_avatar.png'" 
							width="80rpx" 
							height="80rpx" 
							radius="50%"
							:show-error="false"
							class="grid-avatar">
						</custom-image>
						<view class="grid-name xxs">{{ user.nickname || '匿名' }}</view>
					</view>
				</view>
				
				<!-- 更多按钮 -->
				<view class="grid-item" v-if="moreButtonShow">
					<view class="grid-more" @tap="handleToggleExpand">
						<view class="more-icon">
							<text class="more-text">{{ expanded ? '-' : '+' }}</text>
						</view>
						<view class="grid-name xxs">{{ expanded ? '收起' : '更多' }}</view>
					</view>
				</view>
				
				<!-- 空位填充 -->
				<view class="grid-item grid-empty" v-for="n in emptySlots" :key="'empty-' + n"></view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="displayUsers.length === 0 && totalUsers === 0">
			<view class="empty-icon">
				<image class="icon-lg" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_group.png" style="opacity: 0.3;"></image>
			</view>
			<view class="empty-text xs lighter">暂无用户参与拼团</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'GroupUsers',
	props: {
		// 用户列表数据
		userList: {
			type: Array,
			default: () => []
		},
		// 最大显示数量
		maxDisplay: {
			type: Number,
			default: 5
		},
		// 是否显示组件
		show: {
			type: Boolean,
			default: true
		},
		// 布局模式：list(列表) | grid(网格)
		layout: {
			type: String,
			default: 'list'
		},
		// 网格行数
		gridRows: {
			type: Number,
			default: 2
		},
		// 网格列数
		gridCols: {
			type: Number,
			default: 5
		},
		// 是否支持展开
		expandable: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			expanded: false
		};
	},
	computed: {
		// 是否应该显示组件（有数据或者强制显示）
		shouldShow() {
			return this.show && (this.userList.length > 0 || this.userList.length === 0);
		},
		
		// 总用户数
		totalUsers() {
			return this.userList.length;
		},
		
		// 显示的用户列表（列表模式）
		displayUsers() {
			if (this.layout === 'list') {
				return this.userList.slice(0, this.maxDisplay);
			}
			return this.userList;
		},
		
		// 网格模式显示的用户列表
		gridDisplayUsers() {
			if (this.layout !== 'grid') return [];
			
			const maxGridUsers = this.gridRows * this.gridCols - (this.moreButtonShow ? 1 : 0);
			
			if (this.expanded) {
				return this.userList;
			} else {
				return this.userList.slice(0, maxGridUsers);
			}
		},
		
		// 是否显示更多按钮
		moreButtonShow() {
			if (this.layout !== 'grid' || !this.expandable) return false;
			const maxInitialUsers = this.gridRows * this.gridCols - 1; // 减去更多按钮位置
			return this.totalUsers > maxInitialUsers;
		},
		
		// 空位数量（用于网格布局对齐）
		emptySlots() {
			if (this.layout !== 'grid') return 0;
			
			const totalItems = this.gridDisplayUsers.length + (this.moreButtonShow ? 1 : 0);
			const remainder = totalItems % this.gridCols;
			
			return remainder === 0 ? 0 : this.gridCols - remainder;
		}
	},
	methods: {
		// 格式化参团时间
		formatJoinTime(timestamp) {
			if (!timestamp) return '刚刚参团';
			
			const now = Math.floor(Date.now() / 1000);
			const joinTime = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
			const diff = now - joinTime;
			
			if (diff < 60) return '刚刚参团';
			if (diff < 3600) return `${Math.floor(diff / 60)}分钟前参团`;
			if (diff < 86400) return `${Math.floor(diff / 3600)}小时前参团`;
			if (diff < 604800) return `${Math.floor(diff / 86400)}天前参团`;
			
			// 超过一周显示具体日期
			const date = new Date(joinTime * 1000);
			return `${date.getMonth() + 1}月${date.getDate()}日参团`;
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			switch (status) {
				case 1: return 'status-success'; // 已成团
				case 0: return 'status-waiting'; // 拼团中
				case -1: return 'status-failed'; // 已失败
				default: return 'status-waiting';
			}
		},
		
		// 获取状态文本
		getStatusText(status) {
			switch (status) {
				case 1: return '已成团';
				case 0: return '拼团中';
				case -1: return '已失败';
				default: return '拼团中';
			}
		},
		
		// 查看更多用户（列表模式）
		handleViewMore() {
			this.$emit('viewMore', {
				userList: this.userList,
				totalUsers: this.totalUsers
			});
		},
		
		// 切换展开/收起（网格模式）
		handleToggleExpand() {
			this.expanded = !this.expanded;
			this.$emit('toggleExpand', {
				expanded: this.expanded,
				userList: this.userList,
				totalUsers: this.totalUsers
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.group-users {
	border-radius: 20rpx;
	overflow: hidden;
	
	.title-section {
		padding: 24rpx 24rpx 16rpx;
		border-bottom: 1px solid #f5f5f5;
		
		.title-left {
			flex: 1;
			min-width: 0;
		}
		
		.title-icon {
			width: 44rpx;
			height: 44rpx;
			background: linear-gradient(135deg, $-color-primary, #ff6b6b);
			border-radius: 50%;
			margin-right: 16rpx;
			
			.icon-sm {
				filter: brightness(0) invert(1);
			}
		}
		
		.title-content {
			flex: 1;
			min-width: 0;
			
			.title-text {
				color: $-color-normal;
				margin-bottom: 4rpx;
			}
			
			.subtitle {
				color: $-color-muted;
			}
		}
		
		.view-all {
			flex: none;
			color: $-color-primary;
			display: flex;
			align-items: center;
		}
	}
	
	.users-content {
		padding: 8rpx 0;
		
		.user-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 24rpx;
			
			&:not(:last-child) {
				border-bottom: 1px solid #f8f8f8;
			}
			
			.user-info {
				flex: 1;
				min-width: 0;
				
				.user-avatar {
					margin-right: 20rpx;
					flex: none;
				}
				
				.user-details {
					flex: 1;
					min-width: 0;
					
					.username {
						color: $-color-normal;
						margin-bottom: 4rpx;
					}
					
					.join-info {
						color: $-color-lighter;
					}
				}
			}
			
			.user-status {
				flex: none;
				
				.status-tag {
					padding: 6rpx 16rpx;
					border-radius: 20rpx;
					line-height: 1.2;
					
					&.status-success {
						background-color: #f6ffed;
						color: #52c41a;
						border: 1px solid #b7eb8f;
					}
					
					&.status-waiting {
						background-color: #fff7e6;
						color: #fa8c16;
						border: 1px solid #ffd591;
					}
					
					&.status-failed {
						background-color: #fff2f0;
						color: #f5222d;
						border: 1px solid #ffb3a7;
					}
				}
			}
		}
	}
	
	.empty-state {
		padding: 60rpx 24rpx;
		text-align: center;
		
		.empty-icon {
			margin-bottom: 20rpx;
		}
		
		.empty-text {
			color: $-color-lighter;
		}
	}
	
	// 网格布局样式
	.users-grid {
		padding: 24rpx;
		
		.grid-container {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
		}
		
		.grid-item {
			width: calc((100% - 80rpx) / 5); // 5列布局，减去间距
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.grid-user {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 100%;
				
				.grid-avatar {
					margin-bottom: 8rpx;
				}
				
				.grid-name {
					color: $-color-normal;
					text-align: center;
					width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
			
			.grid-more {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 100%;
				cursor: pointer;
				
				.more-icon {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					background: linear-gradient(135deg, $-color-primary, #ff6b6b);
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 8rpx;
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.95);
					}
					
					.more-text {
						color: white;
						font-size: 32rpx;
						font-weight: bold;
						line-height: 1;
					}
				}
				
				.grid-name {
					color: $-color-primary;
					text-align: center;
				}
			}
			
			&.grid-empty {
				visibility: hidden;
			}
		}
	}
}

// 响应式设计适配
@media (max-width: 750rpx) {
	.group-users {
		border-radius: 16rpx;
		
		.title-section {
			padding: 20rpx 20rpx 12rpx;
			
			.title-icon {
				width: 40rpx;
				height: 40rpx;
				margin-right: 12rpx;
			}
		}
		
		.users-content {
			.user-item {
				padding: 14rpx 20rpx;
				
				.user-info {
					.user-avatar {
						margin-right: 16rpx;
					}
				}
			}
		}
		
		.empty-state {
			padding: 50rpx 20rpx;
		}
		
		// 网格布局响应式
		.users-grid {
			padding: 20rpx;
			
			.grid-container {
				gap: 16rpx;
			}
			
			.grid-item {
				width: calc((100% - 64rpx) / 5);
				
				.grid-more .more-icon {
					width: 70rpx;
					height: 70rpx;
					
					.more-text {
						font-size: 28rpx;
					}
				}
			}
		}
	}
}
</style>
