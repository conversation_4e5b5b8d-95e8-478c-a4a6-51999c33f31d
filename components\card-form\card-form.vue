<template>
	<view class="card-form">
		<view class="form-title">填写内容</view>
		<view class="form-content">
			<view class="form-item">
				<view class="label">姓名</view>
				<input 
					class="input" 
					v-model="formData.name" 
					placeholder="请输入姓名"
					@input="onInput"
				/>
			</view>
			<view class="form-item">
				<view class="label">职位</view>
				<input 
					class="input" 
					v-model="formData.position" 
					placeholder="请输入职位"
					@input="onInput"
				/>
			</view>
			<view class="form-item">
				<view class="label">电话</view>
				<input 
					class="input" 
					v-model="formData.phone" 
					placeholder="请输入电话号码"
					type="number"
					@input="onInput"
				/>
			</view>
			<view class="form-item">
				<view class="label">传真</view>
				<input 
					class="input" 
					v-model="formData.fax" 
					placeholder="请输入传真号码"
					@input="onInput"
				/>
			</view>
			<view class="form-item">
				<view class="label">邮箱</view>
				<input 
					class="input" 
					v-model="formData.email" 
					placeholder="请输入邮箱地址"
					@input="onInput"
				/>
			</view>
			<view class="form-item">
				<view class="label">地址</view>
				<input 
					class="input" 
					v-model="formData.address" 
					placeholder="请输入地址"
					@input="onInput"
				/>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CardForm',
	props: {
		value: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			formData: {
				name: '',
				position: '',
				phone: '',
				fax: '',
				email: '',
				address: ''
			}
		}
	},
	watch: {
		value: {
			handler(newVal) {
				this.formData = { ...this.formData, ...newVal };
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		onInput() {
			this.$emit('input', this.formData);
			this.$emit('change', this.formData);
		}
	}
}
</script>

<style lang="scss" scoped>
.card-form {
	background: #fff;
	
	.form-title {
		padding: 24rpx;
		font-size: 32rpx;
		font-weight: bold;
		border-bottom: 1px solid #f0f0f0;
	}
	
	.form-content {
		padding: 0 24rpx;
	}
	
	.form-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1px solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.label {
			width: 120rpx;
			font-size: 28rpx;
			color: #333;
			flex-shrink: 0;
		}
		
		.input {
			flex: 1;
			font-size: 28rpx;
			color: #666;
			margin-left: 20rpx;
		}
	}
}
</style>